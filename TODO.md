1. Make in-level power ups have bloom, and gravitate toward the player. Ensure that the shield resets the shield power up to 30s remaining if the player already has a shield. Currently getting a second shield power up doesn't restore the timer.


2. Add spice- rare bonuses if you complete enough levels e.g. beat level ten and get ricochet rounds that last for that session, etc.
Other ides:
 twin fire (fires two projectiles in parallel instead of one at a time)
"Negative drop": Gas cloud that causes the ship's weapon to malfunction for 3 seconds.


3. Game balance enhancements:
A. Ensure that the EnemyTypeModifier also applies to the enemy spawn rate - i.e. a EnemyType modifer of 1.5 would not only make them 50 faster and higher HP, but also 50% more of them spawn per wave, in that environment. 
B. Adjust the other modifiers magnitude of effect as well. Currently the enemies are relatively unchanging. The waves are the same and the enemies are roughly the same strength and speed even though the environment effects appear to be working and health values are adjusted etc. in custom environments. Make the effects 50% more pronounced and noticable.
C. Reduce the amount of tokens that are rewarded on level completion by 75%.

4. Add weapon variants that can be purchased for WISH - but only one at a time can be equipped. Kinetic Boost increases damage against crystal enemies. Laser is strong against shadow enemies. They should correspond to the different enemy types. Etc. They should have different colors as well. 

Bullet Visibility System
- **Current State Assessment**: Evaluate current bullet visibility and readability
- **Implementation Tasks**:
  - Implement value-based bullet design:
    - Combine bright elements (glowing cores) with dark elements (borders, inner circles)
    - Use low-contrast backgrounds primarily in midtones
  - Optimize bullet colors (favor reds, pinks, purples over yellows/oranges) according to ammo types (flame, ice, etc. corresponding to enemy types).
  - Implement chunking patterns to group bullets into readable formations
  - Implement proper depth sorting:
    - Enemy bullets drawn over player sprites, projectiles, items, and explosions
    - Smaller, faster bullets drawn over larger, slower ones
    - Single bullets drawn over larger chunks

    - **Implementation Tasks**:
  - Add appropriate bullet splash effects on impact


5. **DONE** Improve the look of the game. Make the buttons and styling look more like a sci-fi arcade game.
   Also remove the red circle rendering around the earth enemies. It was part of the default/fallback enemy rendering code. 

5.1 Improve the rest of the UI- the item cards and HUD to have a more modern sci-fi feel.


6. Pre-flight checks:
A. Check the token rewards code to determine what we need to do in order to test with actual tokens. Ensure the game is capable of checking to see how many WISH tokens are in the user's wallet when they close the Genie Menue, prompting the user with their wallet to initiate the transaction, and activating the purchased items only after the transaction is complete.
B. Double-check code that saves to OrangeSDK. 
C. Set up to run on VPS using nginx (we already have other games running on the VPS with nginx) and Upload to VPS, test, fix remaining bugs. Remove debug menu code. Launch token. Go live.


10. Fix enemy weapons. Currently only the fire enemies fire projectiles. Ensure each enemy type fires a corresponding projectile type (like the weapon power ups the player can buy to change ammo types). Ensure each enemy has it's own firing patterns, ammo type, and behavior corresponding to their type. Currently all enemies travel toward the player in rows like an army, and the fire enemies fire directly at the player rather than having a firing PATTERN!!!! FIX IT!



8. **Done** Fix this bug:
handleCollisions() called
GameObject.js:95 Collision check: player (r=32) vs powerup (r=15)
GameObject.js:96   Positions: player=Vector2(615.78, 740.56), powerup=Vector2(404.23, 317.42)
GameObject.js:97   Distance: 473.07615975788696, Collision threshold: 47, Colliding: false
GameObject.js:95 Collision check: player (r=32) vs powerup (r=15)
GameObject.js:96   Positions: player=Vector2(615.78, 740.56), powerup=Vector2(463.15, 356.82)
GameObject.js:97   Distance: 412.97645401188856, Collision threshold: 47, Colliding: false
GameEngine.js:1269 handleCollisions() called
GameEngine.js:1305 Enemy projectile hit player: {damageTaken: 7, health: 0, lives: 0, destroyed: true}
GameEngine.js:1156 Level completed: {levelNumber: 4, completed: false, reason: 'player_destroyed', completionTime: 26.214899999999442, score: 2494, …}
GameEngine.js:1196 Level 4 failed: player_destroyed
GameEngine.js:1145 Level 4 started: {levelNumber: 4, difficulty: 1, totalEnemies: 31, totalWaves: 4, environment: 'space', …}
EnemyManager.js:2280 EnemyManager reset
GameEngine.js:1150 Enemy manager reset for new level
GameEngine.js:1156 Level completed: {levelNumber: 4, completed: false, reason: 'player_destroyed', completionTime: 0.003099999999627471, score: 0, …}
GameEngine.js:1196 Level 4 failed: player_destroyed
GameEngine.js:1145 Level 4 started: {levelNumber: 4, difficulty: 1, totalEnemies: 31, totalWaves: 4, environment: 'space', …}
EnemyManager.js:2280 EnemyManager reset
GameEngine.js:1150 Enemy manager reset for new level
GameEngine.js:1156 Level completed: {levelNumber: 4, completed: false, reason: 'player_destroyed', completionTime: 0.013700000000186265, score: 0, …}
GameEngine.js:1196 Level 4 failed: player_destroyed
GameEngine.js:1601 Game data saved to Orange SDK (auto_save)
GameEngine.js:1145 Level 4 started: {levelNumber: 4, difficulty: 1, totalEnemies: 31, totalWaves: 4, environment: 'space', …}
EnemyManager.js:2280 EnemyManager reset
GameEngine.js:1150 Enemy manager reset for new level
GameEngine.js:1156 Level completed: {levelNumber: 4, completed: false, reason: 'player_destroyed', completionTime: 0.007099999999627471, score: 0, …}
GameEngine.js:1196 Level 4 failed: player_destroyed
GameEngine.js:1145 Level 4 started: {levelNumber: 4, difficulty: 1, totalEnemies: 31, totalWaves: 4, environment: 'space', …}
EnemyManager.js:2280 EnemyManager reset
GameEngine.js:1150 Enemy manager reset for new level
GameEngine.js:1156 Level completed: {levelNumber: 4, completed: false, reason: 'player_destroyed', completionTime: 0.0075, score: 0, …}
GameEngine.js:1196 Level 4 failed: player_destroyed
GameEngine.js:1145 Level 4 started: {levelNumber: 4, difficulty: 1, totalEnemies: 31, totalWaves: 4, environment: 'space', …}
EnemyManager.js:2280 EnemyManager reset

9. **Fixed** Token distribution NaN bug - Added validation to ensure cost is a valid number before processing environment purchases. Fixed in GenieInterface.js, EnvironmentTracker.js, and server/index.js.
Token balance updated: 42500 WISH tokens
GameEngine.js:1453 Token transaction: Objectamount: 7500balanceAfter: 42500id: "tx_1756744411537_fs7yft83n"metadata: {}reason: "power_up_spread_ammo"timestamp: 1756744411537type: "spent"[[Prototype]]: Object
GameEngine.js:1608 Game data saved to Orange SDK (token_change)
GameEngine.js:1477 Power-up purchased: SPREAD_AMMO for 7500 tokens
GameEngine.js:1440 Token balance updated: NaN WISH tokens
GameEngine.js:1453 Token transaction: Objectamount: undefinedbalanceAfter: NaNid: "tx_1756744416362_hyrg8lf17"metadata: {environmentId: 'env_2_1756743681799', environmentName: 'Goblin World'}reason: "environment_purchase"timestamp: 1756744416362type: "spent"[[Prototype]]: Object
EnvironmentTracker.js:253 Environment env_2_1756743681799 purchased by user_1756530070709_p9rw2v3mc for undefined tokens




  ### 2.3 Power-Up System Redesign
- **Current State Assessment**: Evaluate current power-up mechanics
- **Implementation Tasks**:
  - Implement illusion-based power progression:
    - Small actual damage increases (e.g., 1.1x multiplier per level)
    - Significant visual upgrades to convey power
  - Enhance power-up visual feedback:
    - Increase projectile width and height with power levels
    - Add more visual details to higher-level shots
    - Implement more powerful sound effects for maxed shots

    ### 1.1 Movement System Refinement
- **Current State Assessment**: Review current movement mechanics for consistency and control
- **Implementation Tasks**:
  - Add visual enhancements to improve movement feel:
    - Afterimage trails during movement


    
Cyberpunk: Neon colors, glitch effects, futuristic elements
Dark Mode: High contrast, reduced eye strain
Retro-Futurism: 80s/90s inspired futuristic design
UIGEN X 32b

Complete the projectile type configurations for all enemy types (currently missing earth, crystal, shadow) and ensure they match the visual and behavioral characteristics of corresponding weapon variants.


[{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "',' expected.",
	"source": "ts",
	"startLineNumber": 96,
	"startColumn": 32,
	"endLineNumber": 96,
	"endColumn": 33,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1136",
	"severity": 8,
	"message": "Property assignment expected.",
	"source": "ts",
	"startLineNumber": 96,
	"startColumn": 34,
	"endLineNumber": 96,
	"endColumn": 35,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 98,
	"startColumn": 23,
	"endLineNumber": 98,
	"endColumn": 24,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 99,
	"startColumn": 22,
	"endLineNumber": 99,
	"endColumn": 23,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 100,
	"startColumn": 27,
	"endLineNumber": 100,
	"endColumn": 28,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 101,
	"startColumn": 21,
	"endLineNumber": 101,
	"endColumn": 22,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 101,
	"startColumn": 41,
	"endLineNumber": 101,
	"endColumn": 42,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 101,
	"startColumn": 47,
	"endLineNumber": 101,
	"endColumn": 48,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 103,
	"startColumn": 24,
	"endLineNumber": 103,
	"endColumn": 25,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 104,
	"startColumn": 22,
	"endLineNumber": 104,
	"endColumn": 23,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 105,
	"startColumn": 14,
	"endLineNumber": 105,
	"endColumn": 15,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 106,
	"startColumn": 34,
	"endLineNumber": 106,
	"endColumn": 35,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 108,
	"startColumn": 23,
	"endLineNumber": 108,
	"endColumn": 24,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 109,
	"startColumn": 22,
	"endLineNumber": 109,
	"endColumn": 23,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 110,
	"startColumn": 27,
	"endLineNumber": 110,
	"endColumn": 28,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 111,
	"startColumn": 21,
	"endLineNumber": 111,
	"endColumn": 22,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 111,
	"startColumn": 41,
	"endLineNumber": 111,
	"endColumn": 42,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 111,
	"startColumn": 47,
	"endLineNumber": 111,
	"endColumn": 48,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 113,
	"startColumn": 24,
	"endLineNumber": 113,
	"endColumn": 25,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 114,
	"startColumn": 22,
	"endLineNumber": 114,
	"endColumn": 23,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 115,
	"startColumn": 14,
	"endLineNumber": 115,
	"endColumn": 15,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 116,
	"startColumn": 33,
	"endLineNumber": 116,
	"endColumn": 34,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 118,
	"startColumn": 23,
	"endLineNumber": 118,
	"endColumn": 24,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 119,
	"startColumn": 22,
	"endLineNumber": 119,
	"endColumn": 23,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 120,
	"startColumn": 27,
	"endLineNumber": 120,
	"endColumn": 28,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 121,
	"startColumn": 21,
	"endLineNumber": 121,
	"endColumn": 22,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 121,
	"startColumn": 41,
	"endLineNumber": 121,
	"endColumn": 42,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 121,
	"startColumn": 46,
	"endLineNumber": 121,
	"endColumn": 47,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 123,
	"startColumn": 24,
	"endLineNumber": 123,
	"endColumn": 25,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 124,
	"startColumn": 22,
	"endLineNumber": 124,
	"endColumn": 23,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 134,
	"startColumn": 46,
	"endLineNumber": 134,
	"endColumn": 47,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 151,
	"startColumn": 56,
	"endLineNumber": 151,
	"endColumn": 57,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 174,
	"startColumn": 67,
	"endLineNumber": 174,
	"endColumn": 68,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 205,
	"startColumn": 38,
	"endLineNumber": 205,
	"endColumn": 39,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 227,
	"startColumn": 37,
	"endLineNumber": 227,
	"endColumn": 38,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 244,
	"startColumn": 56,
	"endLineNumber": 244,
	"endColumn": 57,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 281,
	"startColumn": 39,
	"endLineNumber": 281,
	"endColumn": 40,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 325,
	"startColumn": 66,
	"endLineNumber": 325,
	"endColumn": 67,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 378,
	"startColumn": 76,
	"endLineNumber": 378,
	"endColumn": 77,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 407,
	"startColumn": 68,
	"endLineNumber": 407,
	"endColumn": 69,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 433,
	"startColumn": 67,
	"endLineNumber": 433,
	"endColumn": 68,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 460,
	"startColumn": 68,
	"endLineNumber": 460,
	"endColumn": 69,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 485,
	"startColumn": 51,
	"endLineNumber": 485,
	"endColumn": 52,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 508,
	"startColumn": 52,
	"endLineNumber": 508,
	"endColumn": 53,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 527,
	"startColumn": 45,
	"endLineNumber": 527,
	"endColumn": 46,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 559,
	"startColumn": 36,
	"endLineNumber": 559,
	"endColumn": 37,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 575,
	"startColumn": 36,
	"endLineNumber": 575,
	"endColumn": 37,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 600,
	"startColumn": 41,
	"endLineNumber": 600,
	"endColumn": 42,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 611,
	"startColumn": 26,
	"endLineNumber": 611,
	"endColumn": 27,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 627,
	"startColumn": 29,
	"endLineNumber": 627,
	"endColumn": 30,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 635,
	"startColumn": 40,
	"endLineNumber": 635,
	"endColumn": 41,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 652,
	"startColumn": 35,
	"endLineNumber": 652,
	"endColumn": 36,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 673,
	"startColumn": 47,
	"endLineNumber": 673,
	"endColumn": 48,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 704,
	"startColumn": 35,
	"endLineNumber": 704,
	"endColumn": 36,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 741,
	"startColumn": 38,
	"endLineNumber": 741,
	"endColumn": 39,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 775,
	"startColumn": 36,
	"endLineNumber": 775,
	"endColumn": 37,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 799,
	"startColumn": 30,
	"endLineNumber": 799,
	"endColumn": 31,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 827,
	"startColumn": 30,
	"endLineNumber": 827,
	"endColumn": 31,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 848,
	"startColumn": 26,
	"endLineNumber": 848,
	"endColumn": 27,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 868,
	"startColumn": 13,
	"endLineNumber": 868,
	"endColumn": 14,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 887,
	"startColumn": 43,
	"endLineNumber": 887,
	"endColumn": 44,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1005",
	"severity": 8,
	"message": "';' expected.",
	"source": "ts",
	"startLineNumber": 896,
	"startColumn": 21,
	"endLineNumber": 896,
	"endColumn": 22,
	"origin": "extHost1"
},{
	"resource": "/home/<USER>/Downloads/AIGames/OrangeDefense/src/systems/EnemyProjectileSystem.js",
	"owner": "typescript",
	"code": "1128",
	"severity": 8,
	"message": "Declaration or statement expected.",
	"source": "ts",
	"startLineNumber": 905,
	"startColumn": 1,
	"endLineNumber": 905,
	"endColumn": 2,
	"origin": "extHost1"
}]